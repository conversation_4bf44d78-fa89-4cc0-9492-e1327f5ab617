#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Verification script for MaxPower fix
"""

import struct

def verify_maxpower_position():
    """Verify the MaxPower reading position and byte order"""
    
    file_path = "./data/0053_20250805_172705.part1.spectrum"
    
    try:
        with open(file_path, 'rb') as file:
            # Read Length at offset 72 + 10MB
            file.seek(72 + 10 * 1024 * 1024)
            length_bytes = file.read(2)
            length_raw = struct.unpack('<H', length_bytes)[0]
            length = ((length_raw & 0xff) << 8 | (length_raw & 0xff00) >> 8)
            
            # Go to first data packet
            file.seek(74 + 10 * 1024 * 1024 + length)
            packet_data = file.read(1976)
            
            print("=" * 60)
            print("🔍 MaxPower Position Verification")
            print("=" * 60)
            
            # GPS coordinates at offset 1968
            gps_offset = 1968
            lat = struct.unpack('>f', packet_data[gps_offset:gps_offset+4])[0]
            lon = struct.unpack('>f', packet_data[gps_offset+4:gps_offset+8])[0]
            
            print(f"GPS coordinates at offset {gps_offset}:")
            print(f"  Latitude:  {lat:.6f}°")
            print(f"  Longitude: {lon:.6f}°")
            
            # MaxPower at offset 1968 - 32 = 1936
            maxpower_offset = gps_offset - 32
            max_power_be = struct.unpack('>f', packet_data[maxpower_offset:maxpower_offset+4])[0]
            max_power_le = struct.unpack('<f', packet_data[maxpower_offset:maxpower_offset+4])[0]
            
            print(f"\nMaxPower at offset {maxpower_offset} (GPS - 32 bytes):")
            print(f"  Big-endian:    {max_power_be:.2f} dBm")
            print(f"  Little-endian: {max_power_le:.2f} dBm")
            
            # Check which one is more reasonable
            if -120 <= max_power_be <= 10:
                print(f"  ✓ Big-endian value is in reasonable range")
            else:
                print(f"  ✗ Big-endian value is out of range")
                
            if -120 <= max_power_le <= 10:
                print(f"  ✓ Little-endian value is in reasonable range")
            else:
                print(f"  ✗ Little-endian value is out of range")
            
            # Check nearby offsets for comparison
            print(f"\nNearby values around GPS coordinates:")
            for offset_delta in [-36, -32, -28, -24, -20, -16, -12, -8, -4]:
                check_offset = gps_offset + offset_delta
                if check_offset >= 0:
                    try:
                        val_be = struct.unpack('>f', packet_data[check_offset:check_offset+4])[0]
                        val_le = struct.unpack('<f', packet_data[check_offset:check_offset+4])[0]
                        
                        # Mark the MaxPower position
                        marker = " ← MaxPower" if offset_delta == -32 else ""
                        
                        print(f"  Offset {check_offset:4d} (GPS{offset_delta:+3d}): BE={val_be:8.2f}, LE={val_le:8.2f}{marker}")
                    except:
                        print(f"  Offset {check_offset:4d}: Error reading")
            
            # Raw bytes at MaxPower position
            raw_bytes = packet_data[maxpower_offset:maxpower_offset+4]
            print(f"\nRaw bytes at MaxPower position ({maxpower_offset}):")
            print(f"  Hex: {raw_bytes.hex().upper()}")
            print(f"  Bytes: {' '.join(f'{b:02X}' for b in raw_bytes)}")
            
            print(f"\n✅ Verification Summary:")
            print(f"   MaxPower position: GPS offset - 32 bytes = {maxpower_offset}")
            print(f"   Byte order: Big-endian (>f)")
            print(f"   Value: {max_power_be:.2f} dBm")
            print(f"   GPS coordinates: ({lat:.6f}°, {lon:.6f}°)")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    verify_maxpower_position()
